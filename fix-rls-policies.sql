-- Fix RLS infinite recursion issue

-- First, drop all existing policies to start fresh
DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Users can create own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Authors can manage own articles" ON user_profiles;
DROP POLICY IF EXISTS "Enable read access for all users" ON user_profiles;
DROP POLICY IF EXISTS "Enable insert for authenticated users based on user_id" ON user_profiles;
DROP POLICY IF EXISTS "Enable update for users based on user_id" ON user_profiles;
DROP POLICY IF EXISTS "Enable all for service role" ON user_profiles;

-- Temporarily disable RLS to allow profile creation
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;

-- Re-enable RLS
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;

-- Create simple, non-conflicting policies
CREATE POLICY "Allow public read access" ON user_profiles 
    FOR SELECT USING (true);

CREATE POLICY "Allow authenticated insert" ON user_profiles 
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow users to update own profile" ON user_profiles 
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Allow users to delete own profile" ON user_profiles 
    FOR DELETE USING (auth.uid() = id);

-- Grant permissions to handle the trigger function
GRANT ALL ON user_profiles TO postgres;
GRANT ALL ON user_profiles TO service_role;

-- Update the trigger function to use SECURITY DEFINER properly
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS trigger AS $$
BEGIN
    -- Log the trigger execution
    RAISE LOG 'Creating profile for user: %', NEW.id;
    
    -- Check if profile already exists
    IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE id = NEW.id) THEN
        INSERT INTO user_profiles (id, full_name, avatar_url, role)
        VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email, 'User'),
            COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
            'user'
        );
        RAISE LOG 'Profile created for user: %', NEW.id;
    ELSE
        RAISE LOG 'Profile already exists for user: %', NEW.id;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
        -- Don't fail the auth process if profile creation fails
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER SET search_path = public;
