import { supabase } from '@/lib/supabase';

export const createProfileForAuthUser = async (userId: string) => {
  try {
    console.log('Creating profile manually for user:', userId);
    
    // Get the auth user data
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      console.error('Could not get auth user:', userError);
      return null;
    }
    
    // Check if profile already exists
    const { data: existingProfile } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('id', userId)
      .maybeSingle();
    
    if (existingProfile) {
      console.log('Profile already exists');
      return existingProfile;
    }
    
    // Create the profile
    const fullName = user.user_metadata?.full_name || user.email || 'User';
    const avatarUrl = user.user_metadata?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.email}`;
    
    const { data, error } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        full_name: fullName,
        avatar_url: avatarUrl,
        role: 'user'
      })
      .select()
      .single();
    
    if (error) {
      console.error('Error creating profile:', error);
      throw error;
    }
    
    console.log('Profile created successfully:', data);
    return data;
    
  } catch (error) {
    console.error('Manual profile creation failed:', error);
    throw error;
  }
};

export const checkAndCreateProfile = async () => {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.log('No authenticated user');
      return null;
    }
    
    console.log('Checking profile for user:', user.id);
    
    // Check if profile exists
    const { data: profile } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .maybeSingle();
    
    if (profile) {
      console.log('Profile exists:', profile);
      return profile;
    }
    
    console.log('No profile found, creating...');
    return await createProfileForAuthUser(user.id);
    
  } catch (error) {
    console.error('Error in checkAndCreateProfile:', error);
    return null;
  }
};
