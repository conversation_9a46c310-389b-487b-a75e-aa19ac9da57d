import { supabase } from '@/lib/supabase';

export const testDatabaseConnection = async () => {
  try {
    console.log('Testing database connection...');
    
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('user_profiles')
      .select('count')
      .limit(1);
    
    if (testError) {
      console.error('Database connection test failed:', testError);
      return false;
    }
    
    console.log('Database connection successful');
    return true;
  } catch (error) {
    console.error('Database connection error:', error);
    return false;
  }
};

export const testUserProfile = async (userId: string) => {
  try {
    console.log('Testing user profile fetch for:', userId);
    
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Profile fetch error:', error);
      return null;
    }
    
    console.log('Profile found:', data);
    return data;
  } catch (error) {
    console.error('Profile test error:', error);
    return null;
  }
};

export const testAuthState = async () => {
  try {
    console.log('Testing auth state...');
    
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Auth session error:', error);
      return null;
    }
    
    console.log('Auth session:', session ? 'exists' : 'null');
    console.log('User ID:', session?.user?.id || 'null');
    
    return session;
  } catch (error) {
    console.error('Auth test error:', error);
    return null;
  }
};

export const createTestProfile = async (userId: string, email: string) => {
  try {
    console.log('Creating test profile for:', userId);

    const fullName = email.split('@')[0];
    const { data, error } = await supabase
      .from('user_profiles')
      .insert({
        id: userId,
        full_name: fullName,
        avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
        role: 'admin' // Make test user admin
      })
      .select()
      .single();

    if (error) {
      console.error('Profile creation error:', error);
      return null;
    }

    console.log('Profile created:', data);
    return data;
  } catch (error) {
    console.error('Profile creation test error:', error);
    return null;
  }
};

export const debugRegistration = async (email: string, password: string, fullName: string) => {
  console.log('=== REGISTRATION DEBUG START ===');

  try {
    // Step 1: Create auth user
    console.log('1. Creating auth user...');
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
          avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
        },
      },
    });

    if (authError) {
      console.error('Auth creation failed:', authError);
      return { success: false, error: authError.message };
    }

    console.log('✓ Auth user created:', authData.user?.id);

    // Step 3: Check if profile was created by trigger
    if (authData.user) {
      console.log('3. Checking if profile was created by trigger...');
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds

      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', authData.user.id)
        .maybeSingle();

      if (profileError && profileError.code !== 'PGRST116') {
        console.error('Profile check failed:', profileError);
      }

      if (profile) {
        console.log('✓ Profile created by trigger:', profile);
        return { success: true, user: authData.user, profile };
      } else {
        console.log('⚠ Profile not created by trigger, creating manually...');

        // Step 4: Create profile manually
        const { data: manualProfile, error: manualError } = await supabase
          .from('user_profiles')
          .insert({
            id: authData.user.id,
            full_name: fullName,
            avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
            role: 'user'
          })
          .select()
          .single();

        if (manualError) {
          console.error('Manual profile creation failed:', manualError);
          return { success: false, error: 'Profile creation failed: ' + manualError.message };
        }

        console.log('✓ Profile created manually:', manualProfile);
        return { success: true, user: authData.user, profile: manualProfile };
      }
    }

    return { success: false, error: 'No user data returned' };

  } catch (error) {
    console.error('Registration debug error:', error);
    return { success: false, error: (error as Error).message };
  } finally {
    console.log('=== REGISTRATION DEBUG END ===');
  }
};
