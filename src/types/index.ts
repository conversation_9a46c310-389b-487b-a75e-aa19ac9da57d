export interface Article {
  id: string;
  title: string;
  summary: string;
  content: string;
  category: Category;
  image_url: string;
  author_id: string;
  published_at: string;
  is_featured: boolean;
  is_breaking: boolean;
  is_trending: boolean;
  views: number;
  created_at: string;
  updated_at: string;
  // Joined data
  author?: UserProfile;
  comments?: Comment[];
}

export type Category = 'Politics' | 'Tech' | 'World' | 'Sports' | 'Entertainment';

export interface Comment {
  id: string;
  article_id: string;
  user_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  // Joined data
  user_profile?: UserProfile;
}

export interface UserProfile {
  id: string;
  full_name: string;
  avatar_url: string | null;
  role: 'user' | 'admin' | 'editor' | 'writer';
  created_at: string;
  updated_at: string;
}

export interface Bookmark {
  id: string;
  user_id: string;
  article_id: string;
  created_at: string;
  // Joined data
  article?: Article;
}

export interface NewsletterSubscriber {
  id: string;
  email: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Advertisement {
  id: string;
  title: string;
  description: string | null;
  image_url: string;
  link_url: string;
  position: 'header' | 'sidebar' | 'footer' | 'inline' | 'popup';
  is_active: boolean;
  start_date: string;
  end_date: string | null;
  clicks: number;
  impressions: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Joined data
  creator?: UserProfile;
}

export interface WebsiteSetting {
  id: string;
  setting_key: string;
  setting_value: string;
  setting_type: 'text' | 'boolean' | 'number' | 'json';
  description: string | null;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface NewsletterTemplate {
  id: string;
  name: string;
  subject_template: string;
  html_template: string;
  is_default: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Joined data
  creator?: UserProfile;
}

export interface NewsletterCampaign {
  id: string;
  title: string;
  subject: string;
  content: string;
  template_id: string | null;
  sent_at: string | null;
  recipients_count: number;
  opened_count: number;
  clicked_count: number;
  created_by: string;
  created_at: string;
  updated_at: string;
  // Joined data
  creator?: UserProfile;
  template?: NewsletterTemplate;
}