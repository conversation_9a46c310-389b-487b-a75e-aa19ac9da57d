import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Switch } from "@/components/ui/switch";
import { mockArticles } from "@/data/mockData";
import { Category, Advertisement, UserProfile } from "@/types";
import {
  BarChart3,
  PenBox,
  Trash2,
  Users,
  Settings,
  Bell,
  Lock,
  Image,
  Upload,
  FileText,
  UserPlus,
  Search,
  Filter,
  Eye,
  Plus,
  ExternalLink,
  <PERSON><PERSON>ding<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>
} from "lucide-react";
import { <PERSON> } from "react-router-dom";
import {
  articleService,
  advertisementService,
  userService,
  settingsService,
  newsletterService
} from "@/lib/database";
import { toast } from "sonner";

export default function AdminDashboard() {
  // State for articles
  const [articles, setArticles] = useState(mockArticles);
  const [newArticle, setNewArticle] = useState({
    title: "",
    summary: "",
    content: "",
    category: "",
    image_url: "",
    is_featured: false,
    is_breaking: false,
    is_trending: false
  });

  // State for advertisements
  const [advertisements, setAdvertisements] = useState<Advertisement[]>([]);
  const [newAd, setNewAd] = useState({
    title: "",
    description: "",
    image_url: "",
    link_url: "",
    position: "sidebar" as const,
    is_active: true
  });

  // State for users
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(false);

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // Load advertisements
      const adsData = await advertisementService.getAdvertisements();
      setAdvertisements(adsData);
      
      // Load users
      const usersData = await userService.getUsers();
      setUsers(usersData);
      
    } catch (error) {
      console.error('Error loading data:', error);
      toast.error('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateArticle = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const articleData = {
        ...newArticle,
        author_id: 'admin-user-id' // Since we removed auth, use a default
      };
      
      await articleService.createArticle(articleData);
      toast.success("Article created successfully!");
      
      // Reset form
      setNewArticle({
        title: "",
        summary: "",
        content: "",
        category: "",
        image_url: "",
        is_featured: false,
        is_breaking: false,
        is_trending: false
      });
      
      // Reload articles
      const updatedArticles = await articleService.getArticles();
      setArticles(updatedArticles);
      
    } catch (error) {
      console.error('Error creating article:', error);
      toast.error("Failed to create article");
    }
  };

  const handleCreateAd = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const adData = {
        ...newAd,
        created_by: 'admin-user-id' // Since we removed auth, use a default
      };
      
      await advertisementService.createAdvertisement(adData);
      toast.success("Advertisement created successfully!");
      
      // Reset form
      setNewAd({
        title: "",
        description: "",
        image_url: "",
        link_url: "",
        position: "sidebar",
        is_active: true
      });
      
      // Reload ads
      const updatedAds = await advertisementService.getAdvertisements();
      setAdvertisements(updatedAds);
      
    } catch (error) {
      console.error('Error creating advertisement:', error);
      toast.error("Failed to create advertisement");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <BarChart3 className="h-6 w-6 text-[#D32F2F]" />
            <span className="text-lg font-semibold">Admin Dashboard</span>
          </div>
          <div className="flex items-center space-x-4">
            <span className="text-sm">Welcome, Admin</span>
            <Bell className="h-5 w-5 cursor-pointer hover:text-[#D32F2F]" />
            <Link to="/">
              <Button variant="outline">Back to Site</Button>
            </Link>
          </div>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-6">
        <Tabs defaultValue="create" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="create">Create Article</TabsTrigger>
            <TabsTrigger value="manage">Manage Articles</TabsTrigger>
            <TabsTrigger value="ads">Advertisements</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="categories">Categories</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          {/* Create Article Tab */}
          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PenBox className="h-5 w-5" />
                  <span>Create New Article</span>
                </CardTitle>
                <CardDescription>
                  Create and publish a new article to your news website.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleCreateArticle} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Title</label>
                      <Input
                        placeholder="Enter article title"
                        value={newArticle.title}
                        onChange={(e) => setNewArticle({...newArticle, title: e.target.value})}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Category</label>
                      <Select 
                        value={newArticle.category} 
                        onValueChange={(value) => setNewArticle({...newArticle, category: value})}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Politics">Politics</SelectItem>
                          <SelectItem value="Tech">Tech</SelectItem>
                          <SelectItem value="World">World</SelectItem>
                          <SelectItem value="Sports">Sports</SelectItem>
                          <SelectItem value="Entertainment">Entertainment</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Summary</label>
                    <Textarea
                      placeholder="Enter article summary"
                      value={newArticle.summary}
                      onChange={(e) => setNewArticle({...newArticle, summary: e.target.value})}
                      rows={3}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Content</label>
                    <Textarea
                      placeholder="Enter article content"
                      value={newArticle.content}
                      onChange={(e) => setNewArticle({...newArticle, content: e.target.value})}
                      rows={10}
                      required
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Image URL</label>
                    <Input
                      placeholder="Enter image URL"
                      value={newArticle.image_url}
                      onChange={(e) => setNewArticle({...newArticle, image_url: e.target.value})}
                      required
                    />
                  </div>
                  
                  <div className="flex space-x-6">
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={newArticle.is_featured}
                        onCheckedChange={(checked) => setNewArticle({...newArticle, is_featured: checked})}
                      />
                      <label className="text-sm">Featured Article</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={newArticle.is_breaking}
                        onCheckedChange={(checked) => setNewArticle({...newArticle, is_breaking: checked})}
                      />
                      <label className="text-sm">Breaking News</label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={newArticle.is_trending}
                        onCheckedChange={(checked) => setNewArticle({...newArticle, is_trending: checked})}
                      />
                      <label className="text-sm">Trending</label>
                    </div>
                  </div>
                  
                  <Button type="submit" className="w-full bg-[#D32F2F] hover:bg-red-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Create Article
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Other tabs would go here... */}
          <TabsContent value="manage">
            <Card>
              <CardHeader>
                <CardTitle>Manage Articles</CardTitle>
                <CardDescription>View and manage all published articles</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Article management interface would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ads">
            <Card>
              <CardHeader>
                <CardTitle>Advertisement Management</CardTitle>
                <CardDescription>Create and manage advertisements</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Advertisement management interface would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="users">
            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
                <CardDescription>Manage user accounts and permissions</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">User management interface would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories">
            <Card>
              <CardHeader>
                <CardTitle>Category Management</CardTitle>
                <CardDescription>Manage article categories</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Category management interface would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Website Settings</CardTitle>
                <CardDescription>Configure website settings</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-gray-500">Settings interface would go here...</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
