import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Search, Menu, X } from "lucide-react";
import { Link } from "react-router-dom";
import SearchDialog from "@/components/news/SearchDialog";

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  return (
    <header className="border-b bg-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-[#D32F2F] rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">N</span>
            </div>
            <span className="text-xl font-bold text-gray-900">NewsPortal</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              Home
            </Link>
            <Link to="/category/Politics" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              Politics
            </Link>
            <Link to="/category/Tech" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              Tech
            </Link>
            <Link to="/category/World" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              World
            </Link>
            <Link to="/category/Sports" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              Sports
            </Link>
            <Link to="/category/Entertainment" className="text-gray-700 hover:text-[#D32F2F] transition-colors">
              Entertainment
            </Link>
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            <SearchDialog />

            {/* Admin Link */}
            <Link to="/admin">
              <Button variant="outline" size="sm">
                Admin
              </Button>
            </Link>

            {/* Mobile menu button */}
            <button
              className="md:hidden"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            >
              {mobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden py-4 border-t">
            <nav className="flex flex-col space-y-4">
              <Link
                to="/"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Home
              </Link>
              <Link
                to="/category/Politics"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Politics
              </Link>
              <Link
                to="/category/Tech"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Tech
              </Link>
              <Link
                to="/category/World"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                World
              </Link>
              <Link
                to="/category/Sports"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Sports
              </Link>
              <Link
                to="/category/Entertainment"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Entertainment
              </Link>
              <Link
                to="/admin"
                className="text-gray-700 hover:text-[#D32F2F] transition-colors"
                onClick={() => setMobileMenuOpen(false)}
              >
                Admin
              </Link>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}