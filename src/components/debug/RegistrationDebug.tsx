import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { debugRegistration, testDatabaseConnection, testAuthState } from '@/utils/testDatabase';
import { checkAndCreateProfile } from '@/utils/manualProfileCreation';

export const RegistrationDebug = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('testpassword123');
  const [fullName, setFullName] = useState('Test User');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleDebugRegistration = async () => {
    setLoading(true);
    setResult(null);
    
    try {
      const debugResult = await debugRegistration(email, password, fullName);
      setResult(debugResult);
    } catch (error) {
      setResult({ success: false, error: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    setLoading(true);
    try {
      const connected = await testDatabaseConnection();
      setResult({ success: connected, message: connected ? 'Database connected' : 'Database connection failed' });
    } catch (error) {
      setResult({ success: false, error: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  const handleTestAuth = async () => {
    setLoading(true);
    try {
      const session = await testAuthState();
      setResult({ success: !!session, session, message: session ? 'User authenticated' : 'No authenticated user' });
    } catch (error) {
      setResult({ success: false, error: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProfile = async () => {
    setLoading(true);
    try {
      const profile = await checkAndCreateProfile();
      setResult({
        success: !!profile,
        profile,
        message: profile ? 'Profile created/found successfully' : 'Failed to create profile'
      });
    } catch (error) {
      setResult({ success: false, error: (error as Error).message });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Registration Debug Tool</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Email</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Password</label>
              <Input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="password"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">Full Name</label>
              <Input
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                placeholder="Full Name"
              />
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              onClick={handleDebugRegistration} 
              disabled={loading}
              className="flex-1"
            >
              {loading ? 'Testing...' : 'Debug Registration'}
            </Button>
            <Button 
              onClick={handleTestConnection} 
              disabled={loading}
              variant="outline"
            >
              Test DB
            </Button>
            <Button
              onClick={handleTestAuth}
              disabled={loading}
              variant="outline"
            >
              Test Auth
            </Button>
            <Button
              onClick={handleCreateProfile}
              disabled={loading}
              variant="outline"
            >
              Create Profile
            </Button>
          </div>
        </CardContent>
      </Card>

      {result && (
        <Card>
          <CardHeader>
            <CardTitle className={result.success ? 'text-green-600' : 'text-red-600'}>
              {result.success ? '✓ Success' : '✗ Failed'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
