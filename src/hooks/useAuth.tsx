import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session, AuthError } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { testAuthState, testUserProfile, createTestProfile } from '@/utils/testDatabase';

interface UserProfile {
  id: string;
  full_name: string;
  avatar_url: string | null;
  role: string;
}

interface AuthContextType {
  user: User | null;
  profile: UserProfile | null;
  session: Session | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName: string) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<void>;
  updateProfile: (updates: Partial<UserProfile>) => Promise<{ error: Error | null }>;
  refreshProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Set a timeout to prevent infinite loading
    const loadingTimeout = setTimeout(() => {
      setLoading(false);
    }, 10000); // 10 seconds max loading time

    // Get initial session
    supabase.auth.getSession().then(async ({ data: { session } }) => {
      console.log('Initial session loaded:', session?.user?.id || 'No session');
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        console.log('Fetching profile for initial session user:', session.user.id);
        await fetchUserProfile(session.user.id);
      } else {
        console.log('No initial session, setting loading to false');
        setLoading(false);
      }

      clearTimeout(loadingTimeout);
    }).catch((error) => {
      console.error('Error getting initial session:', error);
      setLoading(false);
      clearTimeout(loadingTimeout);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        // For new sign-ups, wait a moment for the trigger to create the profile
        if (event === 'SIGNED_UP') {
          setTimeout(async () => {
            await fetchUserProfile(session.user.id);
          }, 1000);
        } else {
          await fetchUserProfile(session.user.id);
        }
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchUserProfile = async (userId: string, retryCount = 0) => {
    try {
      console.log('Fetching profile for user:', userId);

      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .maybeSingle(); // Use maybeSingle instead of single

      if (error) {
        console.error('Profile fetch error:', error);
        setProfile(null);
        setLoading(false);
        return;
      }

      if (data) {
        console.log('Profile found:', data);
        setProfile(data);
      } else {
        console.log('No profile found for user:', userId);
        setProfile(null);

        // Try to create profile if it doesn't exist and this is the first attempt
        if (retryCount === 0) {
          console.log('Attempting to create profile...');
          try {
            await createUserProfile(userId);
            // Retry fetching after creation
            setTimeout(() => fetchUserProfile(userId, 1), 1000);
            return;
          } catch (createError) {
            console.error('Failed to create profile:', createError);
          }
        }
      }

      setLoading(false);
    } catch (error) {
      console.error('Profile fetch error:', error);
      setProfile(null);
      setLoading(false);
    }
  };

  const createUserProfile = async (userId: string) => {
    try {
      console.log('Creating user profile for:', userId);

      // Get user data from auth
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.error('No authenticated user found');
        return;
      }

      const fullName = user.user_metadata?.full_name || user.email || 'User';
      const avatarUrl = user.user_metadata?.avatar_url || `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`;

      console.log('Profile data:', { userId, fullName });

      // Check if profile already exists
      const { data: existingProfile } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .maybeSingle();

      if (existingProfile) {
        console.log('Profile already exists');
        return;
      }

      const { data, error } = await supabase
        .from('user_profiles')
        .insert({
          id: userId,
          full_name: fullName,
          avatar_url: avatarUrl,
          role: 'user'
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating user profile:', error);
        toast.error('Database error saving new user: ' + error.message);
        throw error;
      } else {
        console.log('User profile created successfully:', data);
      }
    } catch (error) {
      console.error('Profile creation error:', error);
      throw error;
    }
  };

  const signUp = async (email: string, password: string, fullName: string) => {
    try {
      console.log('Starting registration process...');

      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
            avatar_url: `https://api.dicebear.com/7.x/avataaars/svg?seed=${email}`,
          },
        },
      });

      if (error) {
        console.error('Auth signup error:', error);
        toast.error(error.message);
        return { error };
      }

      console.log('Auth user created:', data.user?.id);

      // Always try to create profile manually for immediate confirmation
      if (data.user) {
        console.log('User created, attempting to create profile...');

        // Wait a moment for any triggers to fire
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check if profile exists
        const { data: existingProfile } = await supabase
          .from('user_profiles')
          .select('id')
          .eq('id', data.user.id)
          .maybeSingle();

        if (!existingProfile) {
          console.log('No profile found, creating manually...');
          try {
            await createUserProfile(data.user.id);
          } catch (error) {
            console.error('Manual profile creation failed:', error);
            // Don't fail the registration if profile creation fails
          }
        } else {
          console.log('Profile already exists from trigger');
        }

        // Fetch the profile after creation
        setTimeout(() => {
          fetchUserProfile(data.user!.id);
        }, 1000);
      }

      toast.success('Account created successfully! Please check your email to verify your account.');
      return { error: null };
    } catch (error) {
      console.error('Registration error:', error);
      const authError = error as AuthError;
      toast.error(authError.message || 'Registration failed');
      return { error: authError };
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast.error(error.message);
        return { error };
      }

      toast.success('Signed in successfully!');
      return { error: null };
    } catch (error) {
      const authError = error as AuthError;
      toast.error(authError.message);
      return { error: authError };
    }
  };

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) {
        toast.error(error.message);
        return;
      }
      toast.success('Signed out successfully!');
    } catch (error) {
      console.error('Error signing out:', error);
      toast.error('Error signing out');
    }
  };

  const updateProfile = async (updates: Partial<UserProfile>) => {
    if (!user) {
      return { error: new Error('No user logged in') };
    }

    try {
      const { error } = await supabase
        .from('user_profiles')
        .update(updates)
        .eq('id', user.id);

      if (error) {
        toast.error(error.message);
        return { error: new Error(error.message) };
      }

      // Refresh profile data
      await fetchUserProfile(user.id);
      toast.success('Profile updated successfully!');
      return { error: null };
    } catch (error) {
      const err = error as Error;
      toast.error(err.message);
      return { error: err };
    }
  };

  const refreshProfile = async () => {
    if (user?.id) {
      setLoading(true);
      await fetchUserProfile(user.id);
    }
  };

  const value = {
    user,
    profile,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
