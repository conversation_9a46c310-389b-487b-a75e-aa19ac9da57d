-- Fix registration issues by updating the database trigger and policies

-- First, let's update the trigger function to be more robust
CREATE OR REPLACE FUNCTION handle_new_user()
RET<PERSON>NS trigger AS $$
BEGIN
    -- Log the trigger execution
    RAISE LOG 'Creating profile for user: %', NEW.id;
    
    -- Only create profile if user is confirmed (email verified)
    IF NEW.email_confirmed_at IS NOT NULL THEN
        -- Check if profile already exists
        IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE id = NEW.id) THEN
            INSERT INTO user_profiles (id, username, full_name, avatar_url, role)
            VALUES (
                NEW.id,
                COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
                COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
                COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
                'user'
            );
            RAISE LOG 'Profile created for user: %', NEW.id;
        ELSE
            RAISE LOG 'Profile already exists for user: %', NEW.id;
        END IF;
    ELSE
        RAISE LOG 'User not confirmed yet, skipping profile creation: %', NEW.id;
    END IF;
    
    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
        -- Don't fail the auth process if profile creation fails
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the trigger to handle both INSERT and UPDATE (for email confirmation)
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT OR UPDATE OF email_confirmed_at ON auth.users
    FOR EACH ROW 
    WHEN (NEW.email_confirmed_at IS NOT NULL AND (OLD IS NULL OR OLD.email_confirmed_at IS NULL))
    EXECUTE FUNCTION handle_new_user();

-- Update RLS policies to be more permissive for profile creation
DROP POLICY IF EXISTS "Users can create own profile" ON user_profiles;
CREATE POLICY "Users can create own profile" ON user_profiles 
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Allow users to read their own profile during creation
DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
CREATE POLICY "Users can view own profile" ON user_profiles 
    FOR SELECT USING (auth.uid() = id OR true); -- Allow viewing all profiles for now

-- Make sure the username constraint allows for duplicates during the brief creation window
-- We'll handle uniqueness in the application layer
ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_username_key;

-- Add a unique constraint that ignores case and handles the race condition better
CREATE UNIQUE INDEX IF NOT EXISTS user_profiles_username_unique 
ON user_profiles (LOWER(username)) 
WHERE username IS NOT NULL;

-- Add some helpful functions for debugging
CREATE OR REPLACE FUNCTION check_user_profile(user_id UUID)
RETURNS TABLE(
    profile_exists BOOLEAN,
    auth_user_exists BOOLEAN,
    email_confirmed BOOLEAN,
    profile_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXISTS(SELECT 1 FROM user_profiles WHERE id = user_id) as profile_exists,
        EXISTS(SELECT 1 FROM auth.users WHERE id = user_id) as auth_user_exists,
        COALESCE((SELECT email_confirmed_at IS NOT NULL FROM auth.users WHERE id = user_id), false) as email_confirmed,
        COALESCE((SELECT to_jsonb(user_profiles.*) FROM user_profiles WHERE id = user_id), '{}'::jsonb) as profile_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON user_profiles TO anon, authenticated;
GRANT EXECUTE ON FUNCTION check_user_profile(UUID) TO anon, authenticated;
