-- Remove username system and fix registration issues

-- First, remove username-related constraints and indexes
DROP INDEX IF EXISTS user_profiles_username_unique;
ALTER TABLE user_profiles DROP CONSTRAINT IF EXISTS user_profiles_username_key;

-- Remove username column from user_profiles table
ALTER TABLE user_profiles DROP COLUMN IF EXISTS username;

-- Update the trigger function to work without username
CREATE OR REPLACE FUNCTION handle_new_user()
R<PERSON><PERSON>NS trigger AS $$
BEGIN
    -- Log the trigger execution
    RAISE LOG 'Creating profile for user: %', NEW.id;

    -- Check if profile already exists
    IF NOT EXISTS (SELECT 1 FROM user_profiles WHERE id = NEW.id) THEN
        INSERT INTO user_profiles (id, full_name, avatar_url, role)
        VALUES (
            NEW.id,
            COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email, 'User'),
            COALESCE(NEW.raw_user_meta_data->>'avatar_url', 'https://api.dicebear.com/7.x/avataaars/svg?seed=' || NEW.id::text),
            'user'
        );
        RAISE LOG 'Profile created for user: %', NEW.id;
    ELSE
        RAISE LOG 'Profile already exists for user: %', NEW.id;
    END IF;

    RETURN NEW;
EXCEPTION
    WHEN OTHERS THEN
        RAISE LOG 'Error creating profile for user %: %', NEW.id, SQLERRM;
        -- Don't fail the auth process if profile creation fails
        RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create separate triggers for INSERT and UPDATE to avoid the OLD reference issue
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS on_auth_user_confirmed ON auth.users;

-- Trigger for new user creation (immediate confirmation)
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    WHEN (NEW.email_confirmed_at IS NOT NULL)
    EXECUTE FUNCTION handle_new_user();

-- Trigger for email confirmation (when user confirms email later)
CREATE TRIGGER on_auth_user_confirmed
    AFTER UPDATE ON auth.users
    FOR EACH ROW
    WHEN (NEW.email_confirmed_at IS NOT NULL AND OLD.email_confirmed_at IS NULL)
    EXECUTE FUNCTION handle_new_user();

-- Update RLS policies
DROP POLICY IF EXISTS "Users can create own profile" ON user_profiles;
CREATE POLICY "Users can create own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id OR true);

DROP POLICY IF EXISTS "Users can view all profiles" ON user_profiles;
CREATE POLICY "Users can view all profiles" ON user_profiles
    FOR SELECT USING (true);

-- Add debugging function (updated without username)
CREATE OR REPLACE FUNCTION check_user_profile(user_id UUID)
RETURNS TABLE(
    profile_exists BOOLEAN,
    auth_user_exists BOOLEAN,
    email_confirmed BOOLEAN,
    profile_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        EXISTS(SELECT 1 FROM user_profiles WHERE id = user_id) as profile_exists,
        EXISTS(SELECT 1 FROM auth.users WHERE id = user_id) as auth_user_exists,
        COALESCE((SELECT email_confirmed_at IS NOT NULL FROM auth.users WHERE id = user_id), false) as email_confirmed,
        COALESCE((SELECT to_jsonb(user_profiles.*) FROM user_profiles WHERE id = user_id), '{}'::jsonb) as profile_data;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON user_profiles TO anon, authenticated;
GRANT EXECUTE ON FUNCTION check_user_profile(UUID) TO anon, authenticated;
