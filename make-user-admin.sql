-- Make the test user an admin and create profile if missing

-- First, create profile for the user if it doesn't exist
INSERT INTO public.user_profiles (id, full_name, avatar_url, role)
SELECT 
    '5ea133ba-5eae-4274-bece-ef9729b36013'::uuid as id,
    'Test User' as full_name,
    'https://api.dicebear.com/7.x/avataaars/svg?seed=<EMAIL>' as avatar_url,
    'admin' as role
WHERE NOT EXISTS (
    SELECT 1 FROM public.user_profiles 
    WHERE id = '5ea133ba-5eae-4274-bece-ef9729b36013'::uuid
);

-- Update existing profile to admin if it exists
UPDATE public.user_profiles 
SET role = 'admin' 
WHERE id = '5ea133ba-5eae-4274-bece-ef9729b36013'::uuid;

-- Check the result
SELECT 
    au.id,
    au.email,
    au.email_confirmed_at,
    up.full_name,
    up.role,
    up.created_at
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
WHERE au.id = '5ea133ba-5eae-4274-bece-ef9729b36013'::uuid;
