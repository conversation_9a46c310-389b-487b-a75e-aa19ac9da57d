-- Create profile for the current user (<EMAIL>)

-- First, let's see what users exist in auth
SELECT id, email, email_confirmed_at, created_at 
FROM auth.users 
ORDER BY created_at DESC;

-- Create profile for the user <NAME_EMAIL>
INSERT INTO public.user_profiles (id, full_name, avatar_url, role)
SELECT 
    au.id,
    'Admin User' as full_name,
    'https://api.dicebear.com/7.x/avataaars/svg?seed=' || au.email as avatar_url,
    'admin' as role
FROM auth.users au
WHERE au.email = '<EMAIL>'
AND NOT EXISTS (
    SELECT 1 FROM public.user_profiles up 
    WHERE up.id = au.id
);

-- Also create for any other confirmed users without profiles
INSERT INTO public.user_profiles (id, full_name, avatar_url, role)
SELECT 
    au.id,
    COALESCE(au.raw_user_meta_data->>'full_name', 'User') as full_name,
    'https://api.dicebear.com/7.x/avataaars/svg?seed=' || au.email as avatar_url,
    CASE 
        WHEN au.email = '<EMAIL>' THEN 'admin'
        ELSE 'user'
    END as role
FROM auth.users au
WHERE au.email_confirmed_at IS NOT NULL
AND NOT EXISTS (
    SELECT 1 FROM public.user_profiles up 
    WHERE up.id = au.id
);

-- Check the results
SELECT 
    au.id,
    au.email,
    au.email_confirmed_at,
    up.full_name,
    up.role,
    up.created_at as profile_created
FROM auth.users au
LEFT JOIN public.user_profiles up ON au.id = up.id
ORDER BY au.created_at DESC;
